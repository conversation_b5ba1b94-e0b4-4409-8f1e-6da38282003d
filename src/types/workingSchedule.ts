export interface WorkingSchedule {
  id: number;
  userId: number;
  userName?: string;
  standard_check_in_time: string;
  standard_check_out_time: string;
  late_threshold_minutes: number;
  early_threshold_minutes: number;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  user?: {
    id: number;
    name: string;
  };
}

export interface WorkingScheduleFormData {
  userId: number;
  standard_check_in_time: string;
  standard_check_out_time: string;
  late_threshold_minutes?: number;
  early_threshold_minutes?: number;
}

export interface FormattedWorkingSchedule {
  id: number;
  userId: number;
  userName: string;
  checkInTime: string;
  checkOutTime: string;
  lateThresholdMinutes: number;
  earlyThresholdMinutes: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
